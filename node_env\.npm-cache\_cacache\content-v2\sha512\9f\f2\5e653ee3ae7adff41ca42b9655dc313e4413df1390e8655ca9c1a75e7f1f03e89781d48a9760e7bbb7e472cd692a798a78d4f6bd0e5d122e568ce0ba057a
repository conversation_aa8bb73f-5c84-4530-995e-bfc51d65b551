{"name": "@playwright/mcp", "dist-tags": {"next": "1.52.0-alpha-2025-03-26", "latest": "0.0.31"}, "versions": {"1.52.0-alpha-2025-03-13": {"name": "@playwright/mcp", "version": "1.52.0-alpha-2025-03-13", "dependencies": {"playwright": "1.52.0-alpha-2025-03-13"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.6.1"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "20ff3a6140cdbc33d3e318034dd3d83cab62849e", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-1.52.0-alpha-2025-03-13.tgz", "fileCount": 5, "integrity": "sha512-MCTj+S7CijpYzrRx2+buS2EUhONbW21sYnKu/s6qvQoOOK0VwbsNO53F3rBzA2dAaacc7O8mA+DZLOeSwgHDzQ==", "signatures": [{"sig": "MEUCIQCs/EJ5HQpphuBK+GPDSjNNgAElOP9jUHm26SGAH4G5sQIgNCeB1UnK24QKDmWBj2pT1FN8rQufq52rCAro3NXe1Bk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@1.52.0-alpha-2025-03-13", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16011}, "engines": {"node": ">=18"}}, "1.52.0-alpha-2025-03-14": {"name": "@playwright/mcp", "version": "1.52.0-alpha-2025-03-14", "dependencies": {"playwright": "1.52.0-alpha-2025-03-14"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.6.1"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "1ef7bd53eff7a81fe2b57f083d51a1449779c7f9", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-1.52.0-alpha-2025-03-14.tgz", "fileCount": 5, "integrity": "sha512-F/2BuhYvlmRX90bRdK6B5VFUFfwI4PTQvbYb59YShjENRXJynGM6luodM+kfWyhAVhI6MMsUckh2UEZK329Ing==", "signatures": [{"sig": "MEUCID20qBGSkbKSzGDlOIp0ONT0Gy3dM3U6USoVbTE/FVXWAiEAyg41sA8gSYWerKURYgKwFpbIsmXWq6phoEoUXMaDu54=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@1.52.0-alpha-2025-03-14", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16011}, "engines": {"node": ">=18"}}, "1.52.0-alpha-2025-03-15": {"name": "@playwright/mcp", "version": "1.52.0-alpha-2025-03-15", "dependencies": {"playwright": "1.52.0-alpha-2025-03-15"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.6.1"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "f09ba88dd54d03cbe2e4737bd6769aac885fc991", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-1.52.0-alpha-2025-03-15.tgz", "fileCount": 5, "integrity": "sha512-/rN7mgFW439cQGfX4zb3k46MUj0dxU1DnDWng4s7kgS4/Ix5Ypg1lb9nFZogAud8W80BQxi/3XaXX/ThcMoDfQ==", "signatures": [{"sig": "MEUCIGmP8dyVKFugLsiNQRfBLGmlrGhlAlTlie0jZ5BVk+90AiEAhJvwTxpoFHb99dSLdC0theZC2r/+UgXOidJRtHArY5E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@1.52.0-alpha-2025-03-15", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16011}, "engines": {"node": ">=18"}}, "1.52.0-alpha-2025-03-16": {"name": "@playwright/mcp", "version": "1.52.0-alpha-2025-03-16", "dependencies": {"playwright": "1.52.0-alpha-2025-03-16"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.6.1"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "c1a14472c3df5516c02ee552e8bdd878b154a57c", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-1.52.0-alpha-2025-03-16.tgz", "fileCount": 5, "integrity": "sha512-8Lve8OtWvr35A29splcbQ/d35J8jUh4BVGCqGQ1SLvNtA61+7pGNRsmmftqb/CFv8+8mrsGh8xqHvQbZgNdeZg==", "signatures": [{"sig": "MEQCIC9q4m3OVxHiT65D+2dIzt9LBqGhKjrpsZN/VcZXyVWXAiBT7E9+zoSBCJhY0W18sCc+gmNMuQihHXFRver+Z/gOPQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@1.52.0-alpha-2025-03-16", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16011}, "engines": {"node": ">=18"}}, "1.52.0-alpha-2025-03-17": {"name": "@playwright/mcp", "version": "1.52.0-alpha-2025-03-17", "dependencies": {"playwright": "1.52.0-alpha-2025-03-17"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.6.1"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "46cc416a2e251478f589dd29aea8e26dadb0c7b9", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-1.52.0-alpha-2025-03-17.tgz", "fileCount": 5, "integrity": "sha512-7Vpo+PPJj5ITlMlV6vzUfhe6ySRm2P7amq7OJPEVtt1YG0YCe6fYFsRLIjg/qCaO8j/w4cZ2nn3mabFYRiua/Q==", "signatures": [{"sig": "MEUCIQD2JsAjUVq+bnAAJZ568GUZR2A8ZbeYUxBoodmca6bZwAIgIk12duMNASQzgj24/ofDmhQmwNh4SS/OET0K1i13+Ig=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@1.52.0-alpha-2025-03-17", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16011}, "engines": {"node": ">=18"}}, "1.52.0-alpha-2025-03-18": {"name": "@playwright/mcp", "version": "1.52.0-alpha-2025-03-18", "dependencies": {"playwright": "1.52.0-alpha-2025-03-18"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.6.1"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "a4d29634ba48e298fc9a86fdd601b1b3e0546188", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-1.52.0-alpha-2025-03-18.tgz", "fileCount": 5, "integrity": "sha512-d9Rw/HtlED5ydjwmF+0J4HpGx6ld6knNXPGClcn/IEpLNoGBDNf65vZjUZpF12/DWdTpPVWFIMxEFg2NesyBCA==", "signatures": [{"sig": "MEYCIQDtXe4KiNVLgPUlw1Aw9W/qVhbG7BNXVrXvuAz4vX//iAIhANQdTRJa/TmDfCGYBgD1gcZfZZXSQj5/sU040zWaKyBj", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@1.52.0-alpha-2025-03-18", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16011}, "engines": {"node": ">=18"}}, "1.52.0-alpha-2025-03-19": {"name": "@playwright/mcp", "version": "1.52.0-alpha-2025-03-19", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-2025-03-19", "zod-to-json-schema": "^3.24.4"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.6.1"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "f9d7b2c734f78218d113255c7f8b42bae3e07c0f", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-1.52.0-alpha-2025-03-19.tgz", "fileCount": 7, "integrity": "sha512-wdsxt+XgJ43xdQvu/lHCW1Ppt8fJtnf3ZiaqaRhUBC6hyA4uD8a+98oLRedyvNfVlNuLfyG+H1Fm+xEpolfyNg==", "signatures": [{"sig": "MEUCIB205ZtoMO0orwlbVhbS6HRU72ZEJ5wMp4KH8uPU3xvlAiEA9Ns/hsf4l1sFgZ27IXFhXiHuqWAgBGO1CQmiUFWerqQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@1.52.0-alpha-2025-03-19", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 28785}, "engines": {"node": ">=18"}}, "1.52.0-alpha-1742397713000": {"name": "@playwright/mcp", "version": "1.52.0-alpha-1742397713000", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-1742397713000", "zod-to-json-schema": "^3.24.4"}, "devDependencies": {"@types/node": "^22.13.10", "@modelcontextprotocol/sdk": "^1.6.1"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "6fb616b5210c1d8b21cf314bb3a55b58f919e596", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-1.52.0-alpha-1742397713000.tgz", "fileCount": 7, "integrity": "sha512-AkePeEdmDG+m9EvfIO/5J2+HCl8/5fS4qiimTf3m2g6K72Qf+JANvmHx5xWUKilTKkyaOfigGrSjemy1bPCmhg==", "signatures": [{"sig": "MEQCIHFG9LdyBglYDBTysCa2W+BFqCMccw1+iT4gn/ZzTRUSAiAi9YvH7WOybBEz4ikrOy0n7JHZIRjGi/jMiFq5IPJxMQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@1.52.0-alpha-1742397713000", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 28803}, "engines": {"node": ">=18"}}, "1.52.0-alpha-1742404918000": {"name": "@playwright/mcp", "version": "1.52.0-alpha-1742404918000", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-1742404918000", "zod-to-json-schema": "^3.24.4"}, "devDependencies": {"@types/node": "^22.13.10", "@modelcontextprotocol/sdk": "^1.6.1"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "0f73abc6d98af210a3ff74b567887dcf8d778683", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-1.52.0-alpha-1742404918000.tgz", "fileCount": 7, "integrity": "sha512-FxxG0xGedct7+8zWxnp47WpbusRx7Z8aarK0hy8NYxhkB/ePbb+dtAcqu7SvFhQr+XGe8RwcXZhSFVRy6gWTbA==", "signatures": [{"sig": "MEUCIQDSojexv67SpEcmr6mHMfZ+zuJyh+mWMRt7ymOYw+XdcQIgNkJG8UJvf/xTmTgci6IqmHO9og3NguMlZFRLCHesnrg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@1.52.0-alpha-1742404918000", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 28803}, "engines": {"node": ">=18"}}, "1.52.0-alpha-1742413599000": {"name": "@playwright/mcp", "version": "1.52.0-alpha-1742413599000", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-1742413599000", "zod-to-json-schema": "^3.24.4"}, "devDependencies": {"@types/node": "^22.13.10", "@modelcontextprotocol/sdk": "^1.6.1"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "fc7705c143ded20bca34abb702637a96f8de735e", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-1.52.0-alpha-1742413599000.tgz", "fileCount": 7, "integrity": "sha512-IqBpMEDy/puoYbSTJ6A9z7yVYMcvmfyTtgvm9D/uKVSnf4Sewb/o5WvOx63YmIpBBRsJEh4Z8/uEwZNdwZ0K0A==", "signatures": [{"sig": "MEUCIQCR3PEXHyLXpZVd+YIUzNHDQgjLhmlUC6saKiF1wnZ4RwIgZkDSd6VNA4WIWUn0b0YonJnOgfpfFHFO+P+gWh9Bl7Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@1.52.0-alpha-1742413599000", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 28803}, "engines": {"node": ">=18"}}, "1.52.0-alpha-1742413711000": {"name": "@playwright/mcp", "version": "1.52.0-alpha-1742413711000", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-1742413711000", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"@types/node": "^22.13.10"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "4fa253cc81f806b70b148341ec300b5379ae38ab", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-1.52.0-alpha-1742413711000.tgz", "fileCount": 14, "integrity": "sha512-JvNYxFvpi38BJKQjuFLXbG2XEGbEOgHSaI5v3IZ1fE5uCblhNbkQkFV9M/Dr05K/kkz/YZC6iPJm72AfQAIdMg==", "signatures": [{"sig": "MEYCIQCpSFMTo/tG007ScRwbB9sUHYdsl7Dn0yKmP+XmLliWawIhAOEgJ8onpE8aO2eOMpByDHzOB5feUY40IKPY14vNzHe/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@1.52.0-alpha-1742413711000", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 47289}, "engines": {"node": ">=18"}}, "1.52.0-alpha-2025-03-20": {"name": "@playwright/mcp", "version": "1.52.0-alpha-2025-03-20", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-2025-03-20", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"@types/node": "^22.13.10"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "dd49dad291ccd04a9e7f028c300a131d0a6bc424", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-1.52.0-alpha-2025-03-20.tgz", "fileCount": 14, "integrity": "sha512-nqD3yzKLFYvlIRXQ/gmiyUpH/hGxyB9ZNw/zg/5SJwxgzcZvRpbV5A6zd6gd0f1+t4++W7i+K56M/05MkJGlzA==", "signatures": [{"sig": "MEYCIQCEDO9yp1X+70YexpDyrKwO0HQJiECLWVx5hHwVHfZ6jwIhALUG/vcpabdPPgJwB/jIEYldQuqavqDpm0VoHzN9CgI2", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@1.52.0-alpha-2025-03-20", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 47283}, "engines": {"node": ">=18"}}, "1.52.0-alpha-2025-03-21": {"name": "@playwright/mcp", "version": "1.52.0-alpha-2025-03-21", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-2025-03-21", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"@types/node": "^22.13.10"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "ecfd565ba032d7e551c2e226c381c71e6fb53bf3", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-1.52.0-alpha-2025-03-21.tgz", "fileCount": 14, "integrity": "sha512-njYjqxexmUC+/IB9CFoiYOqp/rB3X8lPZ7lRjT18zQGmo4Q3HBGJQiizQwFh7cQMKGQi+/CNgTSxekeCEhiSVQ==", "signatures": [{"sig": "MEUCIHCK43ZTUXFtBzoy1vJvNlH0L5oxtjUMS/3UekrXvbnHAiEA5sBmuQKCTZntwXt7Dq0AMnxkCh+HyN4AHT0A/xpoFao=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@1.52.0-alpha-2025-03-21", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 47283}, "engines": {"node": ">=18"}}, "0.0.1": {"name": "@playwright/mcp", "version": "0.0.1", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-2025-03-21", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.52.0-alpha-2025-03-21", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "0b63ed542d36df60d1d787a59ebe1f9fa30cafbe", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.1.tgz", "fileCount": 14, "integrity": "sha512-1yM6Lnj1+bvT1YlQLzPJO/4ATFLpmh7WeyH1IkDpxYFsa8y6w8+rB3Z4ec8r13LAJQ+DJmqoY+Tq3D7fbB/fkw==", "signatures": [{"sig": "MEUCIQC5kwfPoTvxW5RAqZybhbHk0ndWx7VWekie9uSbtgmUBAIgZwFY4JLoU7QU9kXyLB3CiqNm4PcVw1GLRsB2Vnb2Xp0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52135}, "engines": {"node": ">=18"}}, "0.0.2": {"name": "@playwright/mcp", "version": "0.0.2", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-2025-03-21", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.52.0-alpha-2025-03-21", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "815bae5de28bf2a4ee13cabb14feb273ecbe39cc", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.2.tgz", "fileCount": 14, "integrity": "sha512-ojwqYjDz1WnVsE0vCvRNgg3TTjXxnX8wmADVe7VHsxlQsiFIdlSBVqA3DBwbKSDStnetgaxDxhE2WTIxvbU62g==", "signatures": [{"sig": "MEUCIEli50QCN8+K4u6v+R7IAFQpaK8BXhKxgYcKfk9WPurQAiEAoTA13HsZ94DiiVPgNEYLI5DiN0G9WRguvKPnmLG642I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52163}, "engines": {"node": ">=18"}}, "1.52.0-alpha-2025-03-22": {"name": "@playwright/mcp", "version": "1.52.0-alpha-2025-03-22", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-2025-03-22", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"@types/node": "^22.13.10"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "c0ca9189c03af2383560e293df7f8e1948d37f2c", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-1.52.0-alpha-2025-03-22.tgz", "fileCount": 14, "integrity": "sha512-UGrywDKdgLbggCULQ7xZvZ0CVs9sgEGsWoWzaNrPeURFXeF7w4dVI5Mf6DeZ65yxbh5I71yQG2BHrqY8QJFiCg==", "signatures": [{"sig": "MEQCIDvahOGSVr8adxoExXQ3B66w3ax3STmNJ6kmOxTYbKA9AiBxd4egbg3tc8Tat3yotanhfo/0AZ03rtyiHq7hwDV0YA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@1.52.0-alpha-2025-03-22", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 47283}, "engines": {"node": ">=18"}}, "1.52.0-alpha-2025-03-23": {"name": "@playwright/mcp", "version": "1.52.0-alpha-2025-03-23", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-2025-03-23", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"@types/node": "^22.13.10"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "4c60dad5409a7975dc820c8bf86f817617e57a4b", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-1.52.0-alpha-2025-03-23.tgz", "fileCount": 14, "integrity": "sha512-CtFrFKxpiTS/MJDMrNLJCCXb+0Dc1QLq2Sxy0b2v2DL1EK2p9zli20aal4m96IP6i0Gl04iJzJLDgfGJokAKCA==", "signatures": [{"sig": "MEUCIDwk1Dvxc2y7Rqo5AIWkDrT1/EKTeJ2ClqzOASueBl3TAiEAx0xsDcDJz7TPzZhYcl/81c2ks+F2PZ5UGnmQX3xsT/U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@1.52.0-alpha-2025-03-23", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 47283}, "engines": {"node": ">=18"}}, "1.52.0-alpha-2025-03-24": {"name": "@playwright/mcp", "version": "1.52.0-alpha-2025-03-24", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-2025-03-24", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"@types/node": "^22.13.10"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "74f9e33d012b1c52f484a731484df48ed83b8608", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-1.52.0-alpha-2025-03-24.tgz", "fileCount": 14, "integrity": "sha512-NoATnbQnZgn7rEVrPBvIsxRqAx5sk/c0xILsCTcxhZtchvaeefXMZUHMq7MAQUTJp/anh8TidNDAN33Hjw+QRw==", "signatures": [{"sig": "MEUCIQCmO4Fy/rV/Qywm18zONzIugUSYWn7jJAiAJwUN/LkPgAIgSlxwrBk42nX7fEiJfkrSpjU+CCsT4rpCIgF47CWRTAA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@1.52.0-alpha-2025-03-24", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 47283}, "engines": {"node": ">=18"}}, "1.52.0-alpha-2025-03-25": {"name": "@playwright/mcp", "version": "1.52.0-alpha-2025-03-25", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-2025-03-25", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"@types/node": "^22.13.10"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "1845434244fb399f0d3fb57450822ca3a2091f2e", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-1.52.0-alpha-2025-03-25.tgz", "fileCount": 14, "integrity": "sha512-nZnSijo11BorWjjQk899gYI8vvE3x5QWCN0tk8aUXFJTRm8ycMO+zkmLbjpOjcXdKgCoW4CA7btJBhKNYhPZFw==", "signatures": [{"sig": "MEQCIFRAE3M5Hr7zZNs+p7S/JYLIJ/enw3jKZClAMPYPHPxOAiBCpuEn5daXomN4vLsAve2XIDW+iADevrr0CS04NH7Qig==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@1.52.0-alpha-2025-03-25", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 47283}, "engines": {"node": ">=18"}}, "0.0.3": {"name": "@playwright/mcp", "version": "0.0.3", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-2025-03-21", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.52.0-alpha-2025-03-21", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "81faf66352e878593ae7777eee3bd01899846ed0", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.3.tgz", "fileCount": 14, "integrity": "sha512-YrSqhbTj+xVzuyBDeSjo35HTE8zpd7sjciFFWGRMO9SL1g22qiFAJ5VqB4Oorm13GEInWx4RLd7CtJdYoShttg==", "signatures": [{"sig": "MEYCIQDCd81LYb6yB9lXnELduUqI8G5/jwu6dvdBeMB7a5pzwQIhAMT+eHGMk0yc4R8ct0hLMYVucWBBxPa/7GsoM4qMtnxK", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52844}, "engines": {"node": ">=18"}}, "1.52.0-alpha-2025-03-26": {"name": "@playwright/mcp", "version": "1.52.0-alpha-2025-03-26", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-2025-03-26", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"@types/node": "^22.13.10"}, "bin": {"mcp": "cli.js"}, "dist": {"shasum": "baacb0c86182f3f06b149c3dd33bd667ca8bcfbd", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-1.52.0-alpha-2025-03-26.tgz", "fileCount": 14, "integrity": "sha512-Rh7fKevOoaC/nLg/4nOHlQXd/awaHefRsd83VFS7lX3A4qmsXAcmxAQBqnw5V3nhwVnUIxjSFu4hMear/a+9sw==", "signatures": [{"sig": "MEQCIHwHLgsQjYpRYp9OiFz2yNqdPdpnC14joGKAXcwUuZ0lAiB+aoW7WlIdA+nXDYDKYt/U/E3H17iFnS1BHcgnReNZIQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@1.52.0-alpha-2025-03-26", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 47283}, "engines": {"node": ">=18"}}, "0.0.4": {"name": "@playwright/mcp", "version": "0.0.4", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-1743011787000", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.52.0-alpha-1743011787000", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "761caaff9d1198ecbc79725a92a68bc8232391df", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.4.tgz", "fileCount": 17, "integrity": "sha512-fj2IWIekeQ2uLW3XzzWJzIe6gbv4QhJNOkVPFMzzZVfMBvhAhfi0upPqxk1yiy0vQO5Wsp/6caSzrHjugRTFig==", "signatures": [{"sig": "MEUCIQDPLPqe8e/0h1Wt3v1W0TkHVuG2Xla3CmPAA1cf6cs4DwIgIgHpvkfp5cpuoJRJIhVOXVLAXZ0McbNmxkbZnn+AwhE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 59527}, "engines": {"node": ">=18"}}, "0.0.5": {"name": "@playwright/mcp", "version": "0.0.5", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-1743011787000", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.52.0-alpha-1743011787000", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "5cf2f3c644fa04ac08c278dac08b212a1d7f91ff", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.5.tgz", "fileCount": 17, "integrity": "sha512-vNRgmIpo+9mcI6o9w3ZKujzdCGAu18HLB5MO42d5o7213L7S3OMvhjh1kjZgAgWuKL8UQCEEfCp73OH9XL+P7A==", "signatures": [{"sig": "MEYCIQCFjlCoENumSO17UQ82mNVvMakEaH9s+4ffbAECwsdq8QIhAO64KLzKaM+OOJjJfioHGXT2zNtVnhRaJo8UHSw3xQ88", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 61512}, "engines": {"node": ">=18"}}, "0.0.6": {"name": "@playwright/mcp", "version": "0.0.6", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-1743011787000", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.52.0-alpha-1743011787000", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "172457c984ca94eb49a313d974473570926c8ca8", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.6.tgz", "fileCount": 17, "integrity": "sha512-tw7tvtjleywSFRC0wwqtwDgDHrclyIbxuFG/sHlDssqRj0gNQFENic0jM1bWKUi/cHYvVqqBkmLeHejDpnEUeg==", "signatures": [{"sig": "MEUCIQCTzlka8pxt6BJaqXeUebXGHHYmll3/Etl6oFMF6Ha8cwIgPyDMcGiDvOlNTjYXA1hoICJzWRDnhh0S+o6O2ZGuW2M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 67167}, "engines": {"node": ">=18"}}, "0.0.7": {"name": "@playwright/mcp", "version": "0.0.7", "dependencies": {"commander": "^13.1.0", "playwright": "1.52.0-alpha-1743011787000", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.52.0-alpha-1743011787000", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "18ffbc0c2d92d8b1ba7138b5b0c985abd59c1209", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.7.tgz", "fileCount": 17, "integrity": "sha512-F4Yg7haQnllHRjyzhtdiDWmMdwie9slKC1mLpKkpcXJsdOqF/Qisk1ZSl5dFq2m88pQcbiL/QLi8CJUxJxPS5g==", "signatures": [{"sig": "MEUCIQDofTKMuqcc7IDEGsKGbUL2MhbAnqJypfBaeJTNifrFUQIgAp3yC1HBfbAOtod7W7eNpeLdvc1oIq0aiBWuheTNnhU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 67842}, "engines": {"node": ">=18"}}, "0.0.9": {"name": "@playwright/mcp", "version": "0.0.9", "dependencies": {"yaml": "^2.7.1", "commander": "^13.1.0", "playwright": "^1.52.0-alpha-1743163434000", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "^1.52.0-alpha-1743163434000", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "1da993831f49ccdfdc81a01d69725ecd3ebeea2a", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.9.tgz", "fileCount": 17, "integrity": "sha512-V0UL5XcpKKxDv56mhDZ7mesoSsDHy4aThkXU+TVW5uyvzZ+zAyRXBQT37KUl4I/EM6OxlFJBE+DsvPVJ2cvT8A==", "signatures": [{"sig": "MEQCIQCfR5BHvHP6EMZcjFbjHcSmd1cL3+S6/m90tuNjJqeQewIfNc8WNvq+Og0dPxZ+vP6V3Em6Zatbv5seG+wBSDkDag==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 73514}, "engines": {"node": ">=18"}}, "0.0.10": {"name": "@playwright/mcp", "version": "0.0.10", "dependencies": {"yaml": "^2.7.1", "commander": "^13.1.0", "playwright": "^1.52.0-alpha-1743163434000", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "^1.52.0-alpha-1743163434000", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "e3aed394d6681a3e3588b6e89f91212dfbdb35c2", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.10.tgz", "fileCount": 23, "integrity": "sha512-Zi4Ddi+rLzvcVMgDJHAewbUWak257e1XzH54w+yIggZRL45b4u6pOEA2BXZ+zlDFKY2+rQXF7Iq3gj3CdiWhHg==", "signatures": [{"sig": "MEYCIQDiV8z+a8ovfbfRiY4RiA9pQeaDxh8KA4wT+EWZ/+2IKAIhAMW5StV3grrD/Dvh3yzhly8lbOkUHYacAF4QBHB9tXki", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86354}, "engines": {"node": ">=18"}}, "0.0.13": {"name": "@playwright/mcp", "version": "0.0.13", "dependencies": {"yaml": "^2.7.1", "commander": "^13.1.0", "playwright": "^1.52.0-alpha-1743163434000", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "^1.52.0-alpha-1743163434000", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "04c962c0bd2edeb364019db287ff2c31474e340d", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.13.tgz", "fileCount": 24, "integrity": "sha512-HZpIQpyw56HOuHH7savNph7xuERwzcdNNlOCnfgxuCU+zRuYGLqYiXSbkvUd30im6dvcOIDDTRa8ktJbGXhCgg==", "signatures": [{"sig": "MEUCIQC/VnwF70FWJ1N3qOSKtjjwEc67cs2m7KuyzUA4Uf3r6AIgPXXu5FaFENpbRiW6/dMbpxePU/X+W+CW9qRV4IqMKKk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.13", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 95646}, "engines": {"node": ">=18"}}, "0.0.14": {"name": "@playwright/mcp", "version": "0.0.14", "dependencies": {"yaml": "^2.7.1", "commander": "^13.1.0", "playwright": "^1.52.0-alpha-1743163434000", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "^1.52.0-alpha-1743163434000", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "39987a861ab13dc6a2ff5c0fdd99405a94bcd1a5", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.14.tgz", "fileCount": 26, "integrity": "sha512-DUgqv+DeoxRSrdCNIuM5V6xqWgyqZktjXYb6fCzLAfQ99XgEvBDDNcLnWqY6HnMncTRNMzM0I1Z1tVzY8OSKFg==", "signatures": [{"sig": "MEUCICkh38BvDDKFydxD0497R+Sp3WLQQ5pVnnk2zx8YSrQyAiEAoxp4/x1w0/gEh7aqG6AhrEf476kuJHrxDfqyuQAVEDY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.14", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 115169}, "engines": {"node": ">=18"}}, "0.0.15": {"name": "@playwright/mcp", "version": "0.0.15", "dependencies": {"yaml": "^2.7.1", "commander": "^13.1.0", "playwright": "1.53.0-alpha-1745357020000", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.53.0-alpha-1745357020000", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "2370571d859ee81a802ce821c5e822b1343d9552", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.15.tgz", "fileCount": 27, "integrity": "sha512-pmELvn0BrB1VmB1P83vSM4RTLndWdBvWH7/NA6Lb7MZpI+qjVovCP0MCwgxS9DQOLaI6T98yk4zUJV3HCF6tOw==", "signatures": [{"sig": "MEUCIQDug8qKFvmOOBXyMfWtZDnHL1r3Q/9+ulBgjzvrRcR4owIgY/SVEwtHvi7OpA4kBIa1K84+30XttFogAWPaDo8uKoA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.15", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 114638}, "engines": {"node": ">=18"}}, "0.0.16": {"name": "@playwright/mcp", "version": "0.0.16", "dependencies": {"yaml": "^2.7.1", "commander": "^13.1.0", "playwright": "1.53.0-alpha-2025-04-25", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.10.1"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.53.0-alpha-2025-04-25", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "88fff6c038d8475af0801f68159e53659bfbf9e6", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.16.tgz", "fileCount": 32, "integrity": "sha512-HSyp7xY6V1FjIH+dEC4X3BeOa9kVivD5e8q+L8TJxQj/2LBih94PO9FHYBVv730XVk6VWDLqGQaiFYq3AbBZTg==", "signatures": [{"sig": "MEYCIQD/7azIocpgbaq+M9ZP3YOaJgBX5sLodqMruKLc23W7wAIhAMRJtI8iUbds9PwNVz9SLja6m8rynQIQ2w6aYx8qezxA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.16", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 125226}, "engines": {"node": ">=18"}}, "0.0.17": {"name": "@playwright/mcp", "version": "0.0.17", "dependencies": {"yaml": "^2.7.1", "commander": "^13.1.0", "playwright": "1.53.0-alpha-2025-04-25", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.10.1"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.53.0-alpha-2025-04-25", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "a4ad46639e00c93dd996693078a84f66aa17a4a5", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.17.tgz", "fileCount": 32, "integrity": "sha512-BK4tM6eKojFmLtKvJl5azNsmrHfOZSZP5Z8hF3fqSCKa8lbHxYFO72r8m0bZm19NUioVU1forMbj+FeXJqFd1w==", "signatures": [{"sig": "MEUCIF3SzFSXJjNm/386oFZHtDJp2BlmB779+bBwxf4F/J34AiEA0JDARjFAqxiE6pw8Ewi7FQx6cVoh1+Oz6lsX1DFWZEg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.17", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 125235}, "engines": {"node": ">=18"}}, "0.0.18": {"name": "@playwright/mcp", "version": "0.0.18", "dependencies": {"yaml": "^2.7.1", "commander": "^13.1.0", "playwright": "1.53.0-alpha-2025-04-25", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.10.1"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.53.0-alpha-2025-04-25", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "59eb27d01791c2086a3095da9512884a0e299c18", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.18.tgz", "fileCount": 32, "integrity": "sha512-k8xbekCO+HDxt0oVLkF1s5TDauSR0DpeVVV6jSgwvjBSe6j4B9TAu1C1MnE6xOX8IWrHART5CJPFEq1wI4T3DQ==", "signatures": [{"sig": "MEUCIEKWYjLopJbflO5NKApWbmn32m9mqo/uxk+DdImWTKoTAiEAohG5thPebZ/33LdJ1nlkQg6s63S04yUJPRn0ohNrEjU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.18", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 125571}, "engines": {"node": ">=18"}}, "0.0.19": {"name": "@playwright/mcp", "version": "0.0.19", "dependencies": {"yaml": "^2.7.1", "commander": "^13.1.0", "playwright": "1.53.0-alpha-1746218818000", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.10.1"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.53.0-alpha-1746218818000", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "ea621419fa332c2fc71a9eba62b5ffd4c1074143", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.19.tgz", "fileCount": 33, "integrity": "sha512-4Rkjq8rgH4o3O/9XfuX0EzDAtmMPviSj+tFRQsdUgXwzwYJHcFM5H0q3OClHFxS6Dxke2DI/SsEZp6S/ASdjcA==", "signatures": [{"sig": "MEYCIQDym+seADZbvuyC5GoyoBQfCwHdqNWr+cWt5/5UxFZftgIhAJWbaU37RVe3mtxiEQn2Sq5NauMm/YT/X3LusNTTqPXf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.19", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 118863}, "engines": {"node": ">=18"}}, "0.0.20": {"name": "@playwright/mcp", "version": "0.0.20", "dependencies": {"yaml": "^2.7.1", "commander": "^13.1.0", "playwright": "1.53.0-alpha-1746218818000", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.10.1"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.53.0-alpha-1746218818000", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "ffb17a3ffe9a8e1904e64ebbdd28f6ff7b53a504", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.20.tgz", "fileCount": 33, "integrity": "sha512-DoU01GlYMYp3POdcxaZ5y3ziIv297ifFUwRKn+yKLGO2Jsl1Il+I1AyKzHoCnrYG8eu8vZn7JPxmVU4pA+JTdQ==", "signatures": [{"sig": "MEYCIQD7wT0TPoQvjKsOsO7f1zxxvA8jHvuM69nJJLgzqsKUXAIhAN+uO+uIRJs6MtcY+gI40ruwtYxOuPr1YOPo6G6rxzvZ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.20", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 118956}, "engines": {"node": ">=18"}}, "0.0.22": {"name": "@playwright/mcp", "version": "0.0.22", "dependencies": {"yaml": "^2.7.1", "commander": "^13.1.0", "playwright": "1.53.0-alpha-1746218818000", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.11.0"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.53.0-alpha-1746218818000", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "181538e27c8aa003463e10278a96600adeae396d", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.22.tgz", "fileCount": 34, "integrity": "sha512-mn7IQkrAEj1Myi4gUvbUAAVjm+bzp7C7m1xJ2RgR28fHqtrtmf7xyJuDzMX33G72/WaLIBXvQHE/Wvp+QCjJTA==", "signatures": [{"sig": "MEYCIQCUvX+ecn/OlrXcO3BQaxYEi8WOlQQDYtxQnoIH37WsXgIhALZlDIKHuX1aYrmyBclCxIha1roYkgwNuKW+VlSrQBzq", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.22", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 127249}, "engines": {"node": ">=18"}}, "0.0.24": {"name": "@playwright/mcp", "version": "0.0.24", "dependencies": {"commander": "^13.1.0", "playwright": "1.53.0-alpha-1746832516000", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.11.0"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.53.0-alpha-1746832516000", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "51dcda87199cea123590aed869e8757d7d78549c", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.24.tgz", "fileCount": 34, "integrity": "sha512-Hl04KAKddI8UDRXAXDOUt/mtShs0ANYenRoLfZYhlh8ACOTOKMwOa4TA3DLEqprJExxBRyhA7jDyaS2MQUtyPg==", "signatures": [{"sig": "MEUCIDg3zDZONhVL4U0CoTaZ1mAC/qbV1N6a6+R6wnbti3VgAiEAjvis+dwrqhXU9mOUFGn3Y9ph8hmm4HRDxYRvWvSFtqQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.24", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 127110}, "engines": {"node": ">=18"}}, "0.0.25": {"name": "@playwright/mcp", "version": "0.0.25", "dependencies": {"commander": "^13.1.0", "playwright": "1.53.0-alpha-1746832516000", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.11.0"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.53.0-alpha-1746832516000", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "1dd81c8a45facd067e82dcfc90d7f40e1019e11c", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.25.tgz", "fileCount": 36, "integrity": "sha512-8TYb2vuaQFkRLZXYZgAs+Y+A/+L0vRBX+B2gE6o+SJqI85iN1dFxh802rGgZqqBBWlys7UrpJTVd0dh619aCUw==", "signatures": [{"sig": "MEUCIQCGKdZo++sHIQNvHWxfDwSepHzy8K8nlZ+c3ws0RxzpyQIgOZVpp76etgA5AJ+B1ztGQdldoBW4szEOO0Oy6swqZNk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.25", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 136863}, "engines": {"node": ">=18"}}, "0.0.26": {"name": "@playwright/mcp", "version": "0.0.26", "dependencies": {"commander": "^13.1.0", "playwright": "1.53.0-alpha-1746832516000", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.11.0"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.53.0-alpha-1746832516000", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "e71773edd45995af009ba2f781f541ce3dc91faf", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.26.tgz", "fileCount": 36, "integrity": "sha512-xVUBnXwGiHMOGvBg0wri5cfLAZxS6ThI3SWACZQYoIJ/R51JgpTQYtUCMTrg9lhY7BVgdwwn1hCRN4+DW5F1Yg==", "signatures": [{"sig": "MEUCIQCBYnWzxE0JC2X1jRy9O+D3YurPf4q8+ygMNS3qHL72eAIgGccS3+uaJ0Thgms220KE1NNwy2tTX8KutlsAwqdl6c8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.26", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 139599}, "engines": {"node": ">=18"}}, "0.0.27": {"name": "@playwright/mcp", "version": "0.0.27", "dependencies": {"commander": "^13.1.0", "playwright": "1.53.0-alpha-2025-05-27", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.11.0"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.53.0-alpha-2025-05-27", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "579455ea3c47d49c3d45978bac3ec7138c275e65", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.27.tgz", "fileCount": 36, "integrity": "sha512-fBppfTpvOSXDZEpcEunqgmjP7jelzOyx+cp2RRvslWLlt8uoM3+AgQ0bZCo28nUM9A2HdOv8dk+MSvvy2yYhaQ==", "signatures": [{"sig": "MEQCIAsnq/68Di4YlWQMiEeqP4KVYQgu36bwBV8ifUI3potjAiBlNyLxqjYpOMapqht0koH9ceM00cNzB9EKJPCHaf/6xQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.27", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 139984}, "engines": {"node": ">=18"}}, "0.0.28": {"name": "@playwright/mcp", "version": "0.0.28", "dependencies": {"debug": "^4.4.1", "commander": "^13.1.0", "playwright": "1.53.0-alpha-2025-05-27", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.11.0"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@types/debug": "^4.1.12", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.53.0-alpha-2025-05-27", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "f4d89c8cc4ac3fea7fe96d532cf82dee8b9b10b8", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.28.tgz", "fileCount": 39, "integrity": "sha512-PiF8/rPgWM1cptXbRri+m++QVIY4wpMHgKgRQjfzYeb08ORrV2GgdKdI2nlPy9nGFyMGFTA+jc4dDHM/x/7s0A==", "signatures": [{"sig": "MEUCIGxVHJAcgN9kOlhVF65xx8L9t9cnLd+O651d7r6tW+yaAiEAtUJkQPEkxuA8vPmerUprHShtBJBt47M3IpPi7W9Jmbk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.28", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 146833}, "engines": {"node": ">=18"}}, "0.0.29": {"name": "@playwright/mcp", "version": "0.0.29", "dependencies": {"mime": "^4.0.7", "debug": "^4.4.1", "commander": "^13.1.0", "playwright": "1.53.0", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.11.0"}, "devDependencies": {"eslint": "^9.19.0", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@types/debug": "^4.1.12", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.53.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "8e41f5809357b6c5b1e39458bc9a2ffad52199b9", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.29.tgz", "fileCount": 42, "integrity": "sha512-tTwgNsn+xhRVXJ1OhkC34sD/ozFsC4kpZx4l9UwO2BpOIaNNSv72/CezP6Fxigh53VM7NRShptvxk4XRL42tVQ==", "signatures": [{"sig": "MEUCIQCaZDHFH/YsEZmggDyfWxhKg9r5xKLCHlBVKHgXYGRsqQIgHHcEd60q6yjL8Ghx+llK/9yt/osxvSJfUk9IQjlI+Xw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.29", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 163588}, "engines": {"node": ">=18"}}, "0.0.30": {"name": "@playwright/mcp", "version": "0.0.30", "dependencies": {"ws": "^8.18.1", "mime": "^4.0.7", "debug": "^4.4.1", "commander": "^13.1.0", "playwright": "1.54.1", "zod-to-json-schema": "^3.24.4", "@modelcontextprotocol/sdk": "^1.11.0"}, "devDependencies": {"eslint": "^9.19.0", "@types/ws": "^8.18.1", "@eslint/js": "^9.19.0", "typescript": "^5.8.2", "@types/node": "^22.13.10", "@types/debug": "^4.1.12", "@types/chrome": "^0.0.315", "@eslint/eslintrc": "^3.2.0", "@playwright/test": "1.54.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "@stylistic/eslint-plugin": "^3.0.1", "@typescript-eslint/utils": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/eslint-plugin": "^8.26.1"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"shasum": "6fe963bd7e904c852e66efd056c100f734ca167f", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.30.tgz", "fileCount": 41, "integrity": "sha512-ul1MnnGNXN520yqUoDZX1X0UD4E7o2PikdEWM3i7UFxeGgmY9sAHd+tyFioTdrawsyLoGHpvDH33oiTtVRydCg==", "signatures": [{"sig": "MEQCIDmlBDsHEZEKk4sOwX2snKOzkBbeMdgYVBzYaGnhZKlqAiAxrFznFJ0CgYxOP+6EeOVwSGKfZxeR1e7OKvT5rQuQcg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.30", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 165725}, "engines": {"node": ">=18"}}, "0.0.31": {"name": "@playwright/mcp", "version": "0.0.31", "dependencies": {"@modelcontextprotocol/sdk": "^1.11.0", "commander": "^13.1.0", "debug": "^4.4.1", "mime": "^4.0.7", "playwright": "1.55.0-alpha-1752701791000", "playwright-core": "1.55.0-alpha-1752701791000", "ws": "^8.18.1", "zod-to-json-schema": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.19.0", "@playwright/test": "1.55.0-alpha-1752701791000", "@stylistic/eslint-plugin": "^3.0.1", "@types/chrome": "^0.0.315", "@types/debug": "^4.1.12", "@types/node": "^22.13.10", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@typescript-eslint/utils": "^8.26.1", "eslint": "^9.19.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-notice": "^1.0.0", "typescript": "^5.8.2"}, "bin": {"mcp-server-playwright": "cli.js"}, "dist": {"integrity": "sha512-5W025RAL23sybYT4tBA0YVJBTordsr3MlSRcMrKEWwc72hrtXA719vqT6r/Z0QZF+bRecJhgks+0+3410YnKhQ==", "shasum": "2bb4a7749ee864c465b79c159be8d6f31b5c8876", "tarball": "https://registry.npmjs.org/@playwright/mcp/-/mcp-0.0.31.tgz", "fileCount": 41, "unpackedSize": 157172, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@playwright%2fmcp@0.0.31", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDjFKO1i3JDIbMyhzSUnZa/pKrFmthVuur8wSvHAF2TKAiEAhFsOHQOotinq0Tzx6oBT5K1ZupPvjZ3m0Dm182hz0TA="}]}, "engines": {"node": ">=18"}}}, "modified": "2025-07-17T23:07:55.481Z"}